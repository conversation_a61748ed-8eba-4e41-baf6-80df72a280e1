import {
  WebSocketGateway,
  WebSocketServer,
  SubscribeMessage,
  OnGatewayConnection,
  OnGatewayDisconnect,
  WsException,
} from '@nestjs/websockets';
import { Server, Socket } from 'socket.io';
import { JwtService } from '@nestjs/jwt';
import { Logger } from '@nestjs/common';
import { WEBSOCKET_EVENTS } from '@otrs-ai-powered/shared';
import { AuthService } from '../auth/auth.service';

@WebSocketGateway({
  cors: {
    origin: '*',
  },
})
export class ChatGateway implements OnGatewayConnection, OnGatewayDisconnect {
  @WebSocketServer()
  server: Server;

  private readonly logger = new Logger(ChatGateway.name);

  // Map to store user connections
  private readonly userSockets = new Map<string, Set<string>>();

  constructor(
    private readonly jwtService: JwtService,
    private readonly authService: AuthService,
  ) {}

  async handleConnection(client: Socket) {
    const clientId = client.id;
    this.logger.log(`WebSocket connection attempt from client: ${clientId}`);

    try {
      const token = client.handshake.auth.token;
      this.logger.debug(`Token received: ${token ? 'present' : 'missing'}`);

      if (!token) {
        this.logger.warn(`WebSocket connection rejected - no token provided for client: ${clientId}`);
        throw new WsException('Unauthorized - No token provided');
      }

      const user = await this.authenticateToken(token);

      if (!user) {
        this.logger.warn(`WebSocket connection rejected - user not found for client: ${clientId}`);
        throw new WsException('User not found');
      }

      // Store user data in socket
      client.data.user = user;

      // Add socket to user's connections
      this.addUserConnection(user.id, clientId);

      this.logger.log(`WebSocket client connected successfully: ${clientId} for user: ${user.username}`);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      this.logger.error(`WebSocket connection error for client ${clientId}: ${errorMessage}`, error.stack);
      client.disconnect();
    }
  }

  handleDisconnect(client: Socket) {
    const clientId = client.id;
    this.logger.log(`WebSocket client disconnecting: ${clientId}`);

    // Remove socket from user's connections
    if (client.data.user) {
      const userId = client.data.user.id;
      const userSockets = this.userSockets.get(userId);

      if (userSockets) {
        userSockets.delete(clientId);
        this.logger.debug(`Removed connection ${clientId} for user ${userId}`);

        if (userSockets.size === 0) {
          this.userSockets.delete(userId);
          this.logger.debug(`Removed all connections for user ${userId}`);
        }
      }
    }

    this.logger.log(`WebSocket client disconnected: ${clientId}`);
  }

  private async authenticateToken(token: string): Promise<any> {
    if (!token.startsWith('eyJ')) {
      this.logger.warn('Invalid token format - does not start with eyJ');
      throw new WsException('Invalid token format');
    }

    try {
      const parts = token.split('.');
      if (parts.length !== 3) {
        this.logger.warn('Invalid JWT token structure');
        throw new WsException('Invalid token structure');
      }

      const decodedPayload = JSON.parse(atob(parts[1]));
      this.logger.debug(`Token payload decoded for user: ${decodedPayload.sub}`);

      // Handle demo user
      if (decodedPayload.sub === 'demo-user-1') {
        this.logger.debug('Demo user detected, using hardcoded user data');
        return this.createDemoUser();
      }

      // For non-demo tokens, verify with JWT service
      this.logger.debug('Verifying token with JWT service');
      const payload = this.jwtService.verify(token);
      const user = await this.authService.getUserById(payload.sub);

      if (!user) {
        this.logger.warn(`User not found for ID: ${payload.sub}`);
        throw new WsException('User not found');
      }

      return user;
    } catch (jwtError) {
      const errorMessage = jwtError instanceof Error ? jwtError.message : 'Unknown JWT error';
      this.logger.warn(`JWT verification failed: ${errorMessage}`);
      throw new WsException('Invalid token');
    }
  }

  private createDemoUser() {
    return {
      id: 'demo-user-1',
      username: 'demo',
      email: '<EMAIL>',
      roles: ['user'],
      permissions: ['chat:send', 'tickets:create', 'tickets:view'],
      metadata: {
        firstName: 'Demo',
        lastName: 'User',
        isActive: true,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      },
    };
  }

  private addUserConnection(userId: string, clientId: string) {
    if (!this.userSockets.has(userId)) {
      this.userSockets.set(userId, new Set());
    }
    this.userSockets.get(userId)!.add(clientId);
    this.logger.debug(`Added connection ${clientId} for user ${userId}`);
  }

  @SubscribeMessage(WEBSOCKET_EVENTS.MESSAGE)
  handleMessage(_client: Socket, _payload: any) {
    // This is handled by the HTTP endpoint, but we could implement it here as well
    return { event: WEBSOCKET_EVENTS.MESSAGE, data: 'Message received' };
  }

  @SubscribeMessage(WEBSOCKET_EVENTS.TYPING)
  handleTyping(client: Socket, payload: { sessionId: string }) {
    // Broadcast typing event to other users in the same session
    client.broadcast.emit(WEBSOCKET_EVENTS.TYPING, {
      sessionId: payload.sessionId,
      userId: client.data.user.id,
    });

    return { event: WEBSOCKET_EVENTS.TYPING, data: 'Typing event received' };
  }

  // Method to send a message to a specific user
  sendMessageToUser(userId: string, event: string, data: any) {
    const userSockets = this.userSockets.get(userId);

    if (userSockets) {
      for (const socketId of userSockets) {
        this.server.to(socketId).emit(event, data);
      }
    }
  }
}
