import { Controller, Post, Body, UseGuards, Get, Request, UnauthorizedException, Logger } from '@nestjs/common';
import { AuthService } from './auth.service';
import { JwtAuthGuard } from './guards/jwt-auth.guard';
import { AuthCredentials, RefreshTokenRequest } from '@otrs-ai-powered/shared';

@Controller('auth')
export class AuthController {
  private readonly logger = new Logger(AuthController.name);

  constructor(private readonly authService: AuthService) {}

  @Post('login')
  async login(@Body() credentials: AuthCredentials) {
    this.logger.log(`Login attempt for username: ${credentials.username}`);

    try {
      // Validate user credentials
      const user = await this.authService.validateUser(credentials.username, credentials.password);

      if (!user) {
        this.logger.warn(`Login failed for username: ${credentials.username} - Invalid credentials`);
        throw new UnauthorizedException('Invalid credentials');
      }

      // Generate tokens
      const tokenResponse = await this.authService.login(user);
      this.logger.log(`Login successful for username: ${credentials.username}`);

      return tokenResponse;
    } catch (error) {
      this.logger.error(`Login error for username: ${credentials.username}`, error.stack);
      if (error instanceof UnauthorizedException) {
        throw error;
      }
      throw new UnauthorizedException('Authentication failed');
    }
  }

  @Post('logout')
  async logout(@Body() body: { refreshToken: string }) {
    this.logger.log('Logout attempt');

    try {
      await this.authService.logout(body.refreshToken);
      this.logger.log('Logout successful');
      return { message: 'Logged out successfully' };
    } catch (error) {
      this.logger.error('Logout failed', error.stack);
      throw error;
    }
  }

  @Post('refresh-token')
  async refreshToken(@Body() refreshTokenRequest: RefreshTokenRequest) {
    this.logger.log('Refresh token attempt');

    try {
      const result = await this.authService.refreshToken(refreshTokenRequest.refreshToken);
      this.logger.log('Refresh token successful');
      return result;
    } catch (error) {
      this.logger.warn('Refresh token failed', error.message);
      throw new UnauthorizedException('Invalid refresh token');
    }
  }

  @UseGuards(JwtAuthGuard)
  @Get('me')
  async getProfile(@Request() req) {
    this.logger.log(`Profile request for user: ${req.user?.username ?? 'unknown'}`);
    return req.user;
  }
}
